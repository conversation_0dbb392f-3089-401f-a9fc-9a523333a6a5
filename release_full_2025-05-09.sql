-- MySQL dump 10.13  Distrib 9.2.0, for macos15.2 (arm64)
--
-- Host: chinacrm-release-info.cmeep8hwwlid.rds.cn-north-1.amazonaws.com.cn    Database: release
-- ------------------------------------------------------
-- Server version	8.0.40

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `customer`
--

DROP TABLE IF EXISTS `customer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `customer` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `en_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ps_code` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customer`
--

LOCK TABLES `customer` WRITE;
/*!40000 ALTER TABLE `customer` DISABLE KEYS */;
INSERT INTO `customer` VALUES (1,'亿腾','Edding','已上线','https://gitlab.veevadev.com/veevaorion/eddingpharm.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(2,'三生','Sansheng','已上线','https://gitlab.veevadev.com/veevaorion/sansheng.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(3,'百济','Beigene','已上线','https://gitlab.veevadev.com/veevaorion/beigene.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(4,'信达','InnoventBio','已上线','https://gitlab.veevadev.com/veevaorion/innovent.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(5,'诺诚健华','Innocare','已上线','https://gitlab.veevadev.com/veevaorion/innocarepharma.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(6,'萌蒂','Mundipharma','已上线','https://gitlab.veevadev.com/veevaorion/mundipharma.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(7,'亚盛','Ascentage','已上线','https://gitlab.veevadev.com/veevaorion/ascentage.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(8,'诺和诺德','Novo Nordisk','已上线','https://gitlab.veevadev.com/veevaorion/novonordisk.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(9,'艾伯维','AbbVie','已上线','https://gitlab.veevadev.com/veevaorion/abbvie.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(10,'默沙东','MSD','已上线','https://gitlab.veevadev.com/veevaorion/msd.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(11,'百时美施贵宝','BMS','已上线','https://gitlab.veevadev.com/veevaorion/bms.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(12,'科赋','Kenvue','已上线','https://gitlab.veevadev.com/veevaorion/kenvue.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(13,'巴黎欧莱雅','LoReal','已上线','https://gitlab.veevadev.com/veevaorion/loreal.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(14,'默克','Merck','已上线','https://gitlab.veevadev.com/veevaorion/merck.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(15,'爱施健','Aspen','已上线','https://gitlab.veevadev.com/veevaorion/aspen.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(16,'欧莱雅修丽可','Lorealskc','已上线','https://gitlab.veevadev.com/veevaorion/lorealskc.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(17,'强生','J&J IM','已上线','https://gitlab.veevadev.com/veevaorion/jjim.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(18,'杰特贝林','CSL','已上线','https://gitlab.veevadev.com/veevaorion/csl.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(19,'辉瑞','Pfizer','准生产','https://gitlab.veevadev.com/veevaorion/pfizer.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(20,'Veeva01','Veeva01','内部','','2025-05-07 06:50:11','2025-05-07 06:50:11'),(21,'EventCore','EventCore','内部','https://gitlab.veevadev.com/veevaorion/chinasfa-event-core.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(22,'CRMCore','CRMCore','内部','https://gitlab.veevadev.com/veevaorion/chinasfa-crm-core.git','2025-05-07 06:50:11','2025-05-07 06:50:11'),(23,'CRMCIns','CRMCIns','内部','','2025-05-07 06:50:11','2025-05-07 06:50:11'),(24,'CRMSP','CRMSP','内部','https://gitlab.veevadev.com/veevaorion/chinacrm-service-practice.git','2025-05-07 06:50:11','2025-05-07 06:50:11');
/*!40000 ALTER TABLE `customer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `deployment_plan_view`
--

DROP TABLE IF EXISTS `deployment_plan_view`;
/*!50001 DROP VIEW IF EXISTS `deployment_plan_view`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `deployment_plan_view` AS SELECT 
 1 AS `版本号`,
 1 AS `环境名`,
 1 AS `客户名`,
 1 AS `客户英文名`,
 1 AS `是否部署PS代码`,
 1 AS `租户名`,
 1 AS `Service名`,
 1 AS `计划部署日期`,
 1 AS `时间窗口`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `environment`
--

DROP TABLE IF EXISTS `environment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `environment` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `environment`
--

LOCK TABLES `environment` WRITE;
/*!40000 ALTER TABLE `environment` DISABLE KEYS */;
INSERT INTO `environment` VALUES (1,'Prod','2025-05-07 06:50:11','2025-05-07 06:50:11'),(2,'Preview','2025-05-07 06:50:11','2025-05-07 06:50:11');
/*!40000 ALTER TABLE `environment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `jenkins_job_builds`
--

DROP TABLE IF EXISTS `jenkins_job_builds`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `jenkins_job_builds` (
  `id` int NOT NULL AUTO_INCREMENT,
  `job_name` varchar(255) NOT NULL COMMENT 'Jenkins job名称',
  `build_number` int NOT NULL COMMENT 'Jenkins build号',
  `timestamp` bigint NOT NULL COMMENT '触发时间戳',
  `status` varchar(50) NOT NULL COMMENT '构建状态',
  `result` varchar(50) DEFAULT NULL COMMENT '构建结果',
  `build_info` json DEFAULT NULL COMMENT 'Jenkins job执行详细信息JSON',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_job_build` (`job_name`,`build_number`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Jenkins任务构建记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `jenkins_job_builds`
--

LOCK TABLES `jenkins_job_builds` WRITE;
/*!40000 ALTER TABLE `jenkins_job_builds` DISABLE KEYS */;
INSERT INTO `jenkins_job_builds` VALUES (1,'shuaicdev',235,1744267617704,'SUCCESS','SUCCESS','{\"id\": \"235\", \"name\": \"#235\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/wfapi/describe\"}, \"artifacts\": {\"href\": \"/job/shuaicdev/235/wfapi/artifacts\"}, \"changesets\": {\"href\": \"/job/shuaicdev/235/wfapi/changesets\"}}, \"stages\": [{\"id\": \"6\", \"name\": \"Declarative: Checkout SCM\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/6/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 6811, \"startTimeMillis\": 1744267622244, \"pauseDurationMillis\": 0}, {\"id\": \"15\", \"name\": \"Prepare Pipline Params\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/15/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 13718, \"startTimeMillis\": 1744267629215, \"pauseDurationMillis\": 0}, {\"id\": \"61\", \"name\": \"Check env status in EM\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/61/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 667, \"startTimeMillis\": 1744267642971, \"pauseDurationMillis\": 0}, {\"id\": \"70\", \"name\": \"Check Orion image\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/70/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 5976, \"startTimeMillis\": 1744267643689, \"pauseDurationMillis\": 0}, {\"id\": \"95\", \"name\": \"Checkout Infra Code\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/95/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 14873, \"startTimeMillis\": 1744267649705, \"pauseDurationMillis\": 0}, {\"id\": \"117\", \"name\": \"Prepare PS Code\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/117/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 21083, \"startTimeMillis\": 1744267664614, \"pauseDurationMillis\": 0}, {\"id\": \"148\", \"name\": \"Config Variables Mapping\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/148/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 27313, \"startTimeMillis\": 1744267685736, \"pauseDurationMillis\": 0}, {\"id\": \"173\", \"name\": \"Redirect Maintainance Page\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/173/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 14319, \"startTimeMillis\": 1744267713090, \"pauseDurationMillis\": 0}, {\"id\": \"182\", \"name\": \"Stop Orion Services\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/182/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 7428, \"startTimeMillis\": 1744267727463, \"pauseDurationMillis\": 0}, {\"id\": \"191\", \"name\": \"Run Data Migration\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/191/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 289627, \"startTimeMillis\": 1744267734940, \"pauseDurationMillis\": 0}, {\"id\": \"202\", \"name\": \"Run PS Audit\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/202/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 304, \"startTimeMillis\": 1744268024606, \"pauseDurationMillis\": 0}, {\"id\": \"210\", \"name\": \"Deploy Orion Chart\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/210/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 71970, \"startTimeMillis\": 1744268024948, \"pauseDurationMillis\": 0}, {\"id\": \"223\", \"name\": \"Deploy PS Code\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/223/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 275, \"startTimeMillis\": 1744268096956, \"pauseDurationMillis\": 0}, {\"id\": \"231\", \"name\": \"Run Liveness Check\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/231/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 4048, \"startTimeMillis\": 1744268097270, \"pauseDurationMillis\": 0}, {\"id\": \"240\", \"name\": \"Register Build Info\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/240/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 52975, \"startTimeMillis\": 1744268101356, \"pauseDurationMillis\": 0}, {\"id\": \"249\", \"name\": \"Declarative: Post Actions\", \"_links\": {\"self\": {\"href\": \"/job/shuaicdev/235/execution/node/249/wfapi/describe\"}}, \"status\": \"SUCCESS\", \"execNode\": \"\", \"durationMillis\": 2337, \"startTimeMillis\": 1744268154372, \"pauseDurationMillis\": 0}], \"status\": \"SUCCESS\", \"endTimeMillis\": 1744268157060, \"durationMillis\": 539350, \"startTimeMillis\": 1744267617710, \"pauseDurationMillis\": 0, \"queueDurationMillis\": 6}','2025-04-24 09:06:17','2025-04-24 09:06:17');
/*!40000 ALTER TABLE `jenkins_job_builds` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `release_customer_ps_option`
--

DROP TABLE IF EXISTS `release_customer_ps_option`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `release_customer_ps_option` (
  `release_version_id` int NOT NULL,
  `customer_id` int NOT NULL,
  `deploy_ps_code` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0=不部署,1=部署',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`release_version_id`,`customer_id`),
  KEY `customer_id` (`customer_id`),
  CONSTRAINT `release_customer_ps_option_ibfk_1` FOREIGN KEY (`release_version_id`) REFERENCES `release_version` (`id`),
  CONSTRAINT `release_customer_ps_option_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `release_customer_ps_option`
--

LOCK TABLES `release_customer_ps_option` WRITE;
/*!40000 ALTER TABLE `release_customer_ps_option` DISABLE KEYS */;
INSERT INTO `release_customer_ps_option` VALUES (1,3,1,'2025-04-17 07:59:40','2025-05-07 06:50:11'),(1,6,1,'2025-04-17 07:59:40','2025-05-07 06:50:11'),(3,3,1,'2025-05-08 06:17:09','2025-05-08 06:17:09'),(3,8,1,'2025-05-08 06:17:09','2025-05-08 06:17:09'),(3,9,1,'2025-05-08 06:17:09','2025-05-08 06:17:09'),(3,10,1,'2025-05-08 06:17:09','2025-05-08 06:17:09'),(3,13,1,'2025-05-09 03:28:36','2025-05-09 03:28:36'),(3,15,1,'2025-05-08 06:17:09','2025-05-08 06:17:09'),(3,16,1,'2025-05-09 03:28:36','2025-05-09 03:28:36');
/*!40000 ALTER TABLE `release_customer_ps_option` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `release_customers_view`
--

DROP TABLE IF EXISTS `release_customers_view`;
/*!50001 DROP VIEW IF EXISTS `release_customers_view`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `release_customers_view` AS SELECT 
 1 AS `release_version`,
 1 AS `environment`,
 1 AS `customer_name`,
 1 AS `customer_en_name`,
 1 AS `tenant_name`,
 1 AS `release_date`,
 1 AS `tenant_id`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `release_record`
--

DROP TABLE IF EXISTS `release_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `release_record` (
  `id` int NOT NULL AUTO_INCREMENT,
  `release_version_id` int DEFAULT NULL,
  `service_id` int DEFAULT NULL,
  `date` date DEFAULT NULL,
  `time_window` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tenant_id` int DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_release_record_unique` (`release_version_id`,`tenant_id`,`service_id`,`date`),
  KEY `fk_rr_tenant` (`tenant_id`),
  KEY `fk_rr_service` (`service_id`),
  CONSTRAINT `fk_rr_release_version` FOREIGN KEY (`release_version_id`) REFERENCES `release_version` (`id`),
  CONSTRAINT `fk_rr_service` FOREIGN KEY (`service_id`) REFERENCES `service` (`id`),
  CONSTRAINT `fk_rr_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`),
  CONSTRAINT `release_record_ibfk_1` FOREIGN KEY (`release_version_id`) REFERENCES `release_version` (`id`),
  CONSTRAINT `release_record_ibfk_3` FOREIGN KEY (`service_id`) REFERENCES `service` (`id`),
  CONSTRAINT `release_record_ibfk_5` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1399 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `release_record`
--

LOCK TABLES `release_record` WRITE;
/*!40000 ALTER TABLE `release_record` DISABLE KEYS */;
INSERT INTO `release_record` VALUES (1115,1,1,'2025-03-24','22:00-00:00',3,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1116,1,2,'2025-03-24','22:00-00:00',3,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1117,1,3,'2025-03-24','22:00-00:00',3,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1118,1,4,'2025-03-24','22:00-00:00',3,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1119,1,11,'2025-03-24','22:00-00:00',3,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1120,1,1,'2025-03-24','22:00-00:00',4,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1121,1,2,'2025-03-24','22:00-00:00',4,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1122,1,4,'2025-03-24','22:00-00:00',4,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1123,1,11,'2025-03-24','22:00-00:00',4,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1124,1,1,'2025-03-24','19:00-21:00',8,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1125,1,2,'2025-03-24','19:00-21:00',8,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1126,1,3,'2025-03-24','19:00-21:00',8,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1127,1,4,'2025-03-24','19:00-21:00',8,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1128,1,11,'2025-03-24','19:00-21:00',8,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1129,1,1,'2025-03-24','23:00-01:00',9,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1130,1,2,'2025-03-24','23:00-01:00',9,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1131,1,3,'2025-03-24','23:00-01:00',9,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1132,1,4,'2025-03-24','23:00-01:00',9,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1133,1,11,'2025-03-24','23:00-01:00',9,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1134,1,1,'2025-03-27','23:00-01:00',10,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1135,1,2,'2025-03-27','23:00-01:00',10,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1136,1,3,'2025-03-27','23:00-01:00',10,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1137,1,4,'2025-03-27','23:00-01:00',10,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1138,1,11,'2025-03-27','23:00-01:00',10,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1139,1,1,'2025-03-24','22:00-00:00',11,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1140,1,2,'2025-03-24','22:00-00:00',11,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1141,1,3,'2025-03-24','22:00-00:00',11,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1142,1,11,'2025-03-24','22:00-00:00',11,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1143,1,1,'2025-03-24','22:00-00:00',14,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1144,1,2,'2025-03-24','22:00-00:00',14,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1145,1,3,'2025-03-24','22:00-00:00',14,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1146,1,4,'2025-03-24','22:00-00:00',14,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1147,1,11,'2025-03-24','22:00-00:00',14,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1148,1,1,'2025-03-24','22:00-00:00',15,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1149,1,2,'2025-03-24','22:00-00:00',15,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1150,1,3,'2025-03-24','22:00-00:00',15,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1151,1,4,'2025-03-24','22:00-00:00',15,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1152,1,11,'2025-03-24','22:00-00:00',15,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1153,1,1,'2025-03-24','22:00-00:00',48,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1154,1,2,'2025-03-24','22:00-00:00',48,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1155,1,3,'2025-03-24','22:00-00:00',48,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1156,1,4,'2025-03-24','22:00-00:00',48,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1157,1,11,'2025-03-24','22:00-00:00',48,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1158,1,1,'2025-03-24','17:00-19:00',50,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1159,1,2,'2025-03-24','17:00-19:00',50,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1160,1,3,'2025-03-24','17:00-19:00',50,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1161,1,11,'2025-03-24','17:00-19:00',50,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1162,1,1,'2025-03-24','17:00-19:00',52,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1163,1,2,'2025-03-24','17:00-19:00',52,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1164,1,11,'2025-03-24','17:00-19:00',52,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1165,1,1,'2025-03-24','17:00-19:00',53,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1166,1,2,'2025-03-24','17:00-19:00',53,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1167,1,11,'2025-03-24','17:00-19:00',53,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1168,1,1,'2025-03-24','17:00-19:00',54,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1169,1,2,'2025-03-24','17:00-19:00',54,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1170,1,11,'2025-03-24','17:00-19:00',54,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1178,1,1,'2025-03-17','17:00-19:00',18,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1179,1,2,'2025-03-17','17:00-19:00',18,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1180,1,1,'2025-03-17','17:00-19:00',19,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1181,1,2,'2025-03-17','17:00-19:00',19,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1182,1,1,'2025-03-17','17:00-19:00',23,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1183,1,2,'2025-03-17','17:00-19:00',23,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1184,1,3,'2025-03-17','17:00-19:00',23,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1185,1,1,'2025-03-17','17:00-19:00',24,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1186,1,2,'2025-03-17','17:00-19:00',24,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1187,1,1,'2025-03-17','17:00-19:00',25,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1188,1,2,'2025-03-17','17:00-19:00',25,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1189,1,1,'2025-03-17','17:00-19:00',26,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1190,1,2,'2025-03-17','17:00-19:00',26,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1191,1,1,'2025-03-17','17:00-19:00',29,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1192,1,2,'2025-03-17','17:00-19:00',29,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1193,1,3,'2025-03-17','17:00-19:00',29,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1194,1,1,'2025-03-17','17:00-19:00',30,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1195,1,2,'2025-03-17','17:00-19:00',30,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1196,1,1,'2025-03-17','17:00-19:00',32,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1197,1,2,'2025-03-17','17:00-19:00',32,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1198,1,1,'2025-03-17','17:00-19:00',34,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1199,1,2,'2025-03-17','17:00-19:00',34,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1200,1,1,'2025-03-17','17:00-19:00',35,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1201,1,2,'2025-03-17','17:00-19:00',35,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1202,1,3,'2025-03-17','17:00-19:00',35,'2025-05-07 07:36:26','2025-05-07 07:36:26'),(1209,3,1,'2025-05-12','17:00-19:00',18,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1210,3,2,'2025-05-12','17:00-19:00',18,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1211,3,12,'2025-05-12','17:00-19:00',18,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1212,3,1,'2025-05-12','17:00-19:00',19,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1213,3,2,'2025-05-12','17:00-19:00',19,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1214,3,12,'2025-05-12','17:00-19:00',19,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1215,3,1,'2025-05-12','17:00-19:00',23,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1216,3,2,'2025-05-12','17:00-19:00',23,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1217,3,3,'2025-05-12','17:00-19:00',23,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1218,3,12,'2025-05-12','17:00-19:00',23,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1219,3,1,'2025-05-12','17:00-19:00',24,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1220,3,2,'2025-05-12','17:00-19:00',24,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1221,3,12,'2025-05-12','17:00-19:00',24,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1222,3,1,'2025-05-12','17:00-19:00',25,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1223,3,2,'2025-05-12','17:00-19:00',25,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1224,3,12,'2025-05-12','17:00-19:00',25,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1225,3,1,'2025-05-12','17:00-19:00',26,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1226,3,2,'2025-05-12','17:00-19:00',26,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1227,3,12,'2025-05-12','17:00-19:00',26,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1228,3,1,'2025-05-12','17:00-19:00',30,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1229,3,2,'2025-05-12','17:00-19:00',30,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1230,3,12,'2025-05-12','17:00-19:00',30,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1231,3,1,'2025-05-12','17:00-19:00',32,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1232,3,2,'2025-05-12','17:00-19:00',32,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1233,3,12,'2025-05-12','17:00-19:00',32,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1234,3,1,'2025-05-12','17:00-19:00',33,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1235,3,2,'2025-05-12','17:00-19:00',33,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1236,3,12,'2025-05-12','17:00-19:00',33,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1237,3,1,'2025-05-12','17:00-19:00',34,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1238,3,2,'2025-05-12','17:00-19:00',34,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1239,3,12,'2025-05-12','17:00-19:00',34,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1240,3,1,'2025-05-12','17:00-19:00',35,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1241,3,2,'2025-05-12','17:00-19:00',35,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1242,3,3,'2025-05-12','17:00-19:00',35,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1243,3,12,'2025-05-12','17:00-19:00',35,'2025-05-08 06:08:03','2025-05-08 06:08:03'),(1272,3,1,'2025-05-18','22:00-00:00',3,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1273,3,2,'2025-05-18','22:00-00:00',3,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1274,3,3,'2025-05-18','22:00-00:00',3,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1275,3,11,'2025-05-18','22:00-00:00',3,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1276,3,12,'2025-05-18','22:00-00:00',3,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1277,3,1,'2025-05-18','22:00-00:00',4,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1278,3,2,'2025-05-18','22:00-00:00',4,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1279,3,11,'2025-05-18','22:00-00:00',4,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1280,3,12,'2025-05-18','22:00-00:00',4,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1281,3,1,'2025-05-18','19:00-21:00',8,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1282,3,2,'2025-05-18','19:00-21:00',8,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1283,3,3,'2025-05-18','19:00-21:00',8,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1284,3,11,'2025-05-18','19:00-21:00',8,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1285,3,12,'2025-05-18','19:00-21:00',8,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1286,3,1,'2025-05-18','23:00-01:00',9,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1287,3,2,'2025-05-18','23:00-01:00',9,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1288,3,3,'2025-05-18','23:00-01:00',9,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1289,3,11,'2025-05-18','23:00-01:00',9,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1290,3,12,'2025-05-18','23:00-01:00',9,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1291,3,1,'2025-05-25','23:00-01:00',10,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1292,3,2,'2025-05-25','23:00-01:00',10,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1293,3,3,'2025-05-25','23:00-01:00',10,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1294,3,10,'2025-05-25','23:00-01:00',10,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1295,3,11,'2025-05-25','23:00-01:00',10,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1296,3,12,'2025-05-25','23:00-01:00',10,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1297,3,1,'2025-05-18','22:00-00:00',11,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1298,3,2,'2025-05-18','22:00-00:00',11,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1299,3,3,'2025-05-18','22:00-00:00',11,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1300,3,10,'2025-05-18','22:00-00:00',11,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1301,3,11,'2025-05-18','22:00-00:00',11,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1302,3,12,'2025-05-18','22:00-00:00',11,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1303,3,1,'2025-05-18','22:00-00:00',15,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1304,3,2,'2025-05-18','22:00-00:00',15,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1305,3,3,'2025-05-18','22:00-00:00',15,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1306,3,11,'2025-05-18','22:00-00:00',15,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1307,3,12,'2025-05-18','22:00-00:00',15,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1308,3,1,'2025-05-18','22:00-00:00',48,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1309,3,2,'2025-05-18','22:00-00:00',48,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1310,3,3,'2025-05-18','22:00-00:00',48,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1311,3,11,'2025-05-18','22:00-00:00',48,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1312,3,12,'2025-05-18','22:00-00:00',48,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1313,3,1,'2025-05-18','22:00-00:00',49,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1314,3,2,'2025-05-18','22:00-00:00',49,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1315,3,3,'2025-05-18','22:00-00:00',49,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1316,3,11,'2025-05-18','22:00-00:00',49,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1317,3,12,'2025-05-18','22:00-00:00',49,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1318,3,1,'2025-05-18','17:00-19:00',50,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1319,3,2,'2025-05-18','17:00-19:00',50,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1320,3,11,'2025-05-18','17:00-19:00',50,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1321,3,12,'2025-05-18','17:00-19:00',50,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1322,3,1,'2025-05-18','17:00-19:00',52,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1323,3,2,'2025-05-18','17:00-19:00',52,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1324,3,11,'2025-05-18','17:00-19:00',52,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1325,3,12,'2025-05-18','17:00-19:00',52,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1326,3,1,'2025-05-18','17:00-19:00',53,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1327,3,2,'2025-05-18','17:00-19:00',53,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1328,3,11,'2025-05-18','17:00-19:00',53,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1329,3,12,'2025-05-18','17:00-19:00',53,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1330,3,1,'2025-05-18','17:00-19:00',54,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1331,3,2,'2025-05-18','17:00-19:00',54,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1332,3,11,'2025-05-18','17:00-19:00',54,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1333,3,12,'2025-05-18','17:00-19:00',54,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1334,3,1,'2025-05-18','17:00-19:00',63,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1335,3,2,'2025-05-18','17:00-19:00',63,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1336,3,3,'2025-05-18','17:00-19:00',63,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1337,3,11,'2025-05-18','17:00-19:00',63,'2025-05-09 02:59:43','2025-05-09 02:59:43'),(1338,3,12,'2025-05-18','17:00-19:00',63,'2025-05-09 02:59:43','2025-05-09 02:59:43');
/*!40000 ALTER TABLE `release_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `release_service`
--

DROP TABLE IF EXISTS `release_service`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `release_service` (
  `release_version_id` int NOT NULL,
  `service_id` int NOT NULL,
  `environment_id` int NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`release_version_id`,`service_id`,`environment_id`),
  KEY `service_id` (`service_id`),
  KEY `environment_id` (`environment_id`),
  CONSTRAINT `release_service_ibfk_1` FOREIGN KEY (`release_version_id`) REFERENCES `release_version` (`id`),
  CONSTRAINT `release_service_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `service` (`id`),
  CONSTRAINT `release_service_ibfk_3` FOREIGN KEY (`environment_id`) REFERENCES `environment` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `release_service`
--

LOCK TABLES `release_service` WRITE;
/*!40000 ALTER TABLE `release_service` DISABLE KEYS */;
INSERT INTO `release_service` VALUES (1,1,1,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,1,2,'2025-05-07 07:36:14','2025-05-07 07:36:14'),(1,2,1,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,2,2,'2025-05-07 07:36:14','2025-05-07 07:36:14'),(1,3,1,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,3,2,'2025-05-07 07:36:14','2025-05-07 07:36:14'),(1,4,1,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,11,1,'2025-05-07 06:50:11','2025-05-07 06:50:11'),(3,1,1,'2025-05-08 09:51:15','2025-05-08 09:51:15'),(3,1,2,'2025-05-08 06:07:14','2025-05-08 06:07:14'),(3,2,1,'2025-05-08 09:51:15','2025-05-08 09:51:15'),(3,2,2,'2025-05-08 06:07:14','2025-05-08 06:07:14'),(3,3,1,'2025-05-08 09:51:15','2025-05-08 09:51:15'),(3,3,2,'2025-05-08 06:07:14','2025-05-08 06:07:14'),(3,10,1,'2025-05-08 09:51:15','2025-05-08 09:51:15'),(3,10,2,'2025-05-08 06:07:14','2025-05-08 06:07:14'),(3,11,1,'2025-05-08 09:51:15','2025-05-08 09:51:15'),(3,12,1,'2025-05-08 09:51:15','2025-05-08 09:51:15'),(3,12,2,'2025-05-08 06:07:14','2025-05-08 06:07:14');
/*!40000 ALTER TABLE `release_service` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `release_services_view`
--

DROP TABLE IF EXISTS `release_services_view`;
/*!50001 DROP VIEW IF EXISTS `release_services_view`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `release_services_view` AS SELECT 
 1 AS `release_version`,
 1 AS `environment`,
 1 AS `service_name`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `release_tenant`
--

DROP TABLE IF EXISTS `release_tenant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `release_tenant` (
  `release_version_id` int NOT NULL,
  `tenant_id` int NOT NULL,
  `release_date` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`release_version_id`,`tenant_id`),
  KEY `tenant_id` (`tenant_id`),
  CONSTRAINT `release_tenant_ibfk_1` FOREIGN KEY (`release_version_id`) REFERENCES `release_version` (`id`),
  CONSTRAINT `release_tenant_ibfk_2` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `release_tenant`
--

LOCK TABLES `release_tenant` WRITE;
/*!40000 ALTER TABLE `release_tenant` DISABLE KEYS */;
INSERT INTO `release_tenant` VALUES (1,3,'2025-03-24','2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,4,'2025-03-24','2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,8,'2025-03-24','2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,9,'2025-03-24','2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,10,'2025-03-27','2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,11,'2025-03-24','2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,14,'2025-03-24','2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,15,'2025-03-24','2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,18,'2025-03-17','2025-05-07 07:36:14','2025-05-07 07:36:14'),(1,19,'2025-03-17','2025-05-07 07:36:14','2025-05-07 07:36:14'),(1,23,'2025-03-17','2025-05-07 07:36:14','2025-05-07 07:36:14'),(1,24,'2025-03-17','2025-05-07 07:36:14','2025-05-07 07:36:14'),(1,25,'2025-03-17','2025-05-07 07:36:14','2025-05-07 07:36:14'),(1,26,'2025-03-17','2025-05-07 07:36:14','2025-05-07 07:36:14'),(1,29,'2025-03-17','2025-05-07 07:36:14','2025-05-07 07:36:14'),(1,30,'2025-03-17','2025-05-07 07:36:14','2025-05-07 07:36:14'),(1,32,'2025-03-17','2025-05-07 07:36:14','2025-05-07 07:36:14'),(1,34,'2025-03-17','2025-05-07 07:36:14','2025-05-07 07:36:14'),(1,35,'2025-03-17','2025-05-07 07:36:14','2025-05-07 07:36:14'),(1,48,'2025-03-24','2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,50,'2025-03-24','2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,52,'2025-03-24','2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,53,'2025-03-24','2025-05-07 06:50:11','2025-05-07 06:50:11'),(1,54,'2025-03-24','2025-05-07 06:50:11','2025-05-07 06:50:11'),(3,3,'2025-05-18','2025-05-09 02:55:16','2025-05-09 02:55:16'),(3,4,'2025-05-18','2025-05-09 02:55:16','2025-05-09 02:55:16'),(3,8,'2025-05-18','2025-05-09 02:55:16','2025-05-09 02:55:16'),(3,9,'2025-05-18','2025-05-09 02:53:37','2025-05-09 02:55:16'),(3,10,'2025-05-25','2025-05-08 10:05:10','2025-05-09 02:55:41'),(3,11,'2025-05-18','2025-05-09 02:55:16','2025-05-09 02:55:16'),(3,15,'2025-05-18','2025-05-09 02:55:16','2025-05-09 02:55:16'),(3,18,'2025-05-12','2025-05-08 06:07:14','2025-05-08 06:07:14'),(3,19,'2025-05-12','2025-05-08 06:07:14','2025-05-08 06:07:14'),(3,23,'2025-05-12','2025-05-08 06:07:14','2025-05-08 06:07:14'),(3,24,'2025-05-12','2025-05-08 06:07:14','2025-05-08 06:07:14'),(3,25,'2025-05-12','2025-05-08 06:07:14','2025-05-08 06:07:14'),(3,26,'2025-05-12','2025-05-08 06:07:14','2025-05-08 06:07:14'),(3,30,'2025-05-12','2025-05-08 06:07:14','2025-05-08 06:07:14'),(3,32,'2025-05-12','2025-05-08 06:07:14','2025-05-08 06:07:14'),(3,33,'2025-05-12','2025-05-08 06:07:14','2025-05-08 06:07:14'),(3,34,'2025-05-12','2025-05-08 06:07:14','2025-05-08 06:07:14'),(3,35,'2025-05-12','2025-05-08 06:07:14','2025-05-08 06:07:14'),(3,48,'2025-05-18','2025-05-09 02:55:16','2025-05-09 02:55:16'),(3,49,'2025-05-18','2025-05-09 02:55:16','2025-05-09 02:55:16'),(3,50,'2025-05-18','2025-05-09 02:55:16','2025-05-09 02:55:16'),(3,52,'2025-05-18','2025-05-09 02:55:16','2025-05-09 02:55:16'),(3,53,'2025-05-18','2025-05-09 02:55:16','2025-05-09 02:55:16'),(3,54,'2025-05-18','2025-05-09 02:55:16','2025-05-09 02:55:16'),(3,63,'2025-05-18','2025-05-09 02:55:16','2025-05-09 02:55:16');
/*!40000 ALTER TABLE `release_tenant` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `release_version`
--

DROP TABLE IF EXISTS `release_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `release_version` (
  `id` int NOT NULL AUTO_INCREMENT,
  `version` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `release_version`
--

LOCK TABLES `release_version` WRITE;
/*!40000 ALTER TABLE `release_version` DISABLE KEYS */;
INSERT INTO `release_version` VALUES (1,'24R3.3','2025-05-07 06:50:12','2025-05-07 06:50:12'),(2,'25R1.0','2025-05-07 06:50:12','2025-05-07 06:50:12'),(3,'25R1.1','2025-05-07 06:50:12','2025-05-07 06:50:12');
/*!40000 ALTER TABLE `release_version` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service`
--

DROP TABLE IF EXISTS `service`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `is_multi_tenant` tinyint(1) DEFAULT NULL,
  `online_version` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service`
--

LOCK TABLES `service` WRITE;
/*!40000 ALTER TABLE `service` DISABLE KEYS */;
INSERT INTO `service` VALUES (1,'chinacrm',0,NULL,'2025-05-07 06:50:12','2025-05-07 06:50:12'),(2,'canis',1,NULL,'2025-05-07 06:50:12','2025-05-07 06:50:12'),(3,'hydra',0,NULL,'2025-05-07 06:50:12','2025-05-07 06:50:12'),(4,'mintaka',0,NULL,'2025-05-07 06:50:12','2025-05-07 06:50:12'),(5,'taurus',0,NULL,'2025-05-07 06:50:12','2025-05-07 06:50:12'),(6,'rigel',0,NULL,'2025-05-07 06:50:12','2025-05-07 06:50:12'),(7,'pavo',1,NULL,'2025-05-07 06:50:12','2025-05-07 06:50:12'),(8,'aries',1,NULL,'2025-05-07 06:50:12','2025-05-07 06:50:12'),(9,'dataloader',1,NULL,'2025-05-07 06:50:12','2025-05-07 06:50:12'),(10,'lumos',0,NULL,'2025-05-07 06:50:12','2025-05-07 06:50:12'),(11,'em',1,NULL,'2025-05-07 06:50:12','2025-05-07 06:50:12'),(12,'openlog',1,NULL,'2025-05-08 06:05:30','2025-05-08 06:05:30');
/*!40000 ALTER TABLE `service` ENABLE KEYS */;
UNLOCK TABLES;
ALTER DATABASE `release` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`admin`@`%`*/ /*!50003 TRIGGER `after_service_insert` AFTER INSERT ON `service` FOR EACH ROW BEGIN
    IF NEW.is_multi_tenant = 1 THEN
        INSERT INTO release.tenant_service (tenant_id, service_id)
        SELECT id, NEW.id FROM release.tenant;
    END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
ALTER DATABASE `release` CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci ;

--
-- Table structure for table `tenant`
--

DROP TABLE IF EXISTS `tenant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tenant` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `customer_id` int NOT NULL,
  `environment_id` int NOT NULL,
  `deploy_window` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_cust_env` (`customer_id`,`environment_id`),
  KEY `environment_id` (`environment_id`),
  CONSTRAINT `tenant_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`id`),
  CONSTRAINT `tenant_ibfk_2` FOREIGN KEY (`environment_id`) REFERENCES `environment` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=64 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tenant`
--

LOCK TABLES `tenant` WRITE;
/*!40000 ALTER TABLE `tenant` DISABLE KEYS */;
INSERT INTO `tenant` VALUES (1,'Edding',1,1,'22:00-00:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(2,'3sbio/3sbio-guojian',2,1,'22:00-00:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(3,'Beigene',3,1,'22:00-00:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(4,'Innoventbio',4,1,'22:00-00:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(5,'Innocarepharma',5,1,'22:00-00:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(6,'Mundipharma',6,1,'22:00-00:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(7,'Ascentage',7,1,'22:00-00:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(8,'Novoevents',8,1,'19:00-21:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(9,'Abbvie',9,1,'23:00-01:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(10,'msdcn-prod',10,1,'23:00-01:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(11,'bms-prod',11,1,'22:00-00:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(12,'kenvue-prod',12,1,'22:00-00:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(13,'loreal-prod',13,1,'22:00-00:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(14,'merck-prod',14,1,'22:00-00:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(15,'aspen-prod',15,1,'22:00-00:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(16,'SBXEdding',1,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(17,'SBX3sbio',2,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(18,'SBXBeigene',3,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(19,'SBXInnoventbio',4,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(20,'SBXInnocarepharma',5,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(21,'SBXMundipharma',6,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(22,'SBXAscentage',7,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(23,'SBXNovoevents',8,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(24,'SBXAbbvie',9,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(25,'msdcn-presbx',10,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(26,'bms-presbx',11,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(27,'kenvue-presbx',12,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(28,'loreal-presbx',13,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(29,'merck-presbx',14,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(30,'aspen-presbx',15,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(31,'loreals-presbx',16,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(32,'jjim-presbx',17,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(33,'csl-presbx',18,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(34,'pfizer-presbx',19,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(35,'veeva01-prod',20,2,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(47,'loreals-prod',16,1,'22:00-00:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(48,'jjim-prod',17,1,'22:00-00:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(49,'csl-prod',18,1,'22:00-00:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(50,'pfizer-prod',19,1,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(52,'evtcore-prod',21,1,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(53,'crmcore-prod',22,1,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(54,'crmcins-prod',23,1,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12'),(63,'crmsp-prod',24,1,'17:00-19:00','2025-05-07 06:50:12','2025-05-07 06:50:12');
/*!40000 ALTER TABLE `tenant` ENABLE KEYS */;
UNLOCK TABLES;
ALTER DATABASE `release` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`admin`@`%`*/ /*!50003 TRIGGER `after_tenant_insert` AFTER INSERT ON `tenant` FOR EACH ROW BEGIN
    INSERT INTO release.tenant_service (tenant_id, service_id)
    SELECT NEW.id, id FROM release.service WHERE is_multi_tenant = 1;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
ALTER DATABASE `release` CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci ;

--
-- Table structure for table `tenant_service`
--

DROP TABLE IF EXISTS `tenant_service`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tenant_service` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tenant_id` int NOT NULL,
  `service_id` int NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_tenant_service` (`tenant_id`,`service_id`),
  KEY `service_id` (`service_id`),
  CONSTRAINT `tenant_service_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`),
  CONSTRAINT `tenant_service_ibfk_2` FOREIGN KEY (`service_id`) REFERENCES `service` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=571 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tenant_service`
--

LOCK TABLES `tenant_service` WRITE;
/*!40000 ALTER TABLE `tenant_service` DISABLE KEYS */;
INSERT INTO `tenant_service` VALUES (1,3,3,1,'2025-04-16 06:25:38','2025-04-16 06:25:38'),(2,6,3,1,'2025-04-16 06:25:38','2025-04-16 06:25:38'),(3,9,3,1,'2025-04-16 06:25:38','2025-04-16 06:25:38'),(4,10,3,1,'2025-04-16 06:25:38','2025-04-16 06:25:38'),(5,11,3,1,'2025-04-16 06:25:38','2025-04-16 06:25:38'),(6,13,3,1,'2025-04-16 06:25:38','2025-04-16 06:25:38'),(7,14,3,1,'2025-04-16 06:25:38','2025-04-16 06:25:38'),(8,15,3,1,'2025-04-16 06:25:38','2025-04-16 06:25:38'),(9,35,3,0,'2025-04-16 06:25:38','2025-05-08 06:43:36'),(10,47,3,1,'2025-04-16 06:25:38','2025-04-16 06:25:38'),(11,48,3,1,'2025-04-16 06:25:38','2025-04-16 06:25:38'),(12,49,3,1,'2025-04-16 06:25:38','2025-04-16 06:25:38'),(13,50,3,0,'2025-04-16 06:25:38','2025-04-28 06:06:12'),(15,1,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(16,2,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(17,3,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(18,4,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(19,5,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(20,6,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(21,7,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(22,8,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(23,9,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(24,10,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(25,11,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(26,12,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(27,13,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(28,14,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(29,15,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(30,47,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(31,48,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(32,49,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(33,50,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(35,52,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(36,53,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(37,54,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(38,1,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(39,2,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(40,3,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(41,4,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(42,5,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(43,6,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(44,7,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(45,8,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(46,9,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(47,10,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(48,11,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(49,12,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(50,13,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(51,14,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(52,15,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(53,47,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(54,48,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(55,49,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(56,50,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(58,52,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(59,53,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(60,54,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(61,1,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(62,2,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(63,3,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(64,4,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(65,5,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(66,6,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(67,7,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(68,8,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(69,9,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(70,10,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(71,11,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(72,12,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(73,13,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(74,14,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(75,15,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(76,47,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(77,48,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(78,49,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(79,50,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(81,52,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(82,53,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(83,54,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(84,1,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(85,2,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(86,3,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(87,4,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(88,5,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(89,6,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(90,7,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(91,8,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(92,9,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(93,10,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(94,11,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(95,12,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(96,13,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(97,14,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(98,15,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(99,47,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(100,48,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(101,49,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(102,50,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(104,52,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(105,53,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(106,54,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(107,1,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(108,2,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(109,3,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(110,4,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(111,5,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(112,6,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(113,7,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(114,8,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(115,9,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(116,10,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(117,11,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(118,12,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(119,13,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(120,14,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(121,15,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(122,47,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(123,48,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(124,49,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(125,50,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(127,52,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(128,53,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(129,54,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(130,16,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(131,17,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(132,18,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(133,19,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(135,21,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(136,22,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(137,23,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(138,24,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(139,25,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(140,26,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(141,27,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(142,28,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(143,29,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(144,30,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(145,31,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(146,32,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(147,33,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(148,34,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(149,35,2,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(153,16,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(154,17,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(155,18,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(156,19,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(158,21,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(159,22,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(160,23,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(161,24,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(162,25,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(163,26,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(164,27,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(165,28,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(166,29,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(167,30,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(168,31,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(169,32,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(170,33,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(171,34,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(172,35,7,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(176,16,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(177,17,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(178,18,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(179,19,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(181,21,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(182,22,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(183,23,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(184,24,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(185,25,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(186,26,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(187,27,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(188,28,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(189,29,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(190,30,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(191,31,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(192,32,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(193,33,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(194,34,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(195,35,8,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(199,16,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(200,17,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(201,18,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(202,19,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(204,21,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(205,22,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(206,23,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(207,24,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(208,25,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(209,26,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(210,27,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(211,28,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(212,29,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(213,30,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(214,31,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(215,32,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(216,33,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(217,34,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(218,35,9,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(222,16,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(223,17,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(224,18,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(225,19,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(227,21,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(228,22,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(229,23,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(230,24,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(231,25,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(232,26,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(233,27,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(234,28,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(235,29,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(236,30,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(237,31,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(238,32,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(239,33,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(240,34,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(241,35,11,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(245,2,3,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(246,8,3,1,'2025-04-16 06:39:22','2025-04-16 06:39:22'),(247,23,3,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(248,29,3,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(249,1,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(250,2,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(251,3,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(252,4,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(253,5,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(254,6,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(255,7,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(256,8,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(257,9,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(258,10,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(259,11,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(260,12,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(261,13,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(262,14,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(263,15,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(264,47,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(265,48,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(266,49,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(267,50,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(269,52,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(270,53,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(271,54,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(272,16,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(273,17,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(274,18,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(275,19,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(277,21,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(278,22,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(279,23,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(280,24,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(281,25,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(282,26,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(283,27,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(284,28,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(285,29,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(286,30,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(287,31,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(288,32,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(289,33,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(290,34,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(291,35,1,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(295,1,10,0,'2025-04-16 06:39:23','2025-04-28 03:43:23'),(296,1,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(297,1,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(298,1,4,1,'2025-04-16 06:39:23','2025-04-16 09:38:10'),(299,2,10,0,'2025-04-16 06:39:23','2025-04-28 03:47:20'),(300,2,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(301,2,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(302,2,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(303,3,10,0,'2025-04-16 06:39:23','2025-04-28 05:50:55'),(304,3,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(305,3,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(306,3,4,1,'2025-04-16 06:39:23','2025-04-16 09:38:10'),(307,4,10,0,'2025-04-16 06:39:23','2025-04-28 05:50:55'),(308,4,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(309,4,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(310,4,4,1,'2025-04-16 06:39:23','2025-04-16 09:38:10'),(311,5,10,0,'2025-04-16 06:39:23','2025-04-28 05:50:55'),(312,5,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(313,5,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(314,5,4,1,'2025-04-16 06:39:23','2025-04-28 05:56:53'),(315,6,10,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(316,6,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(317,6,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(318,6,4,1,'2025-04-16 06:39:23','2025-04-28 05:57:39'),(319,7,10,0,'2025-04-16 06:39:23','2025-04-28 05:50:55'),(320,7,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(321,7,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(322,7,4,1,'2025-04-16 06:39:23','2025-04-16 09:38:10'),(323,8,10,0,'2025-04-16 06:39:23','2025-04-28 05:50:55'),(324,8,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(325,8,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(326,8,4,1,'2025-04-16 06:39:23','2025-04-16 09:38:10'),(327,9,10,0,'2025-04-16 06:39:23','2025-04-28 05:50:55'),(328,9,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(329,9,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(330,9,4,1,'2025-04-16 06:39:23','2025-04-16 09:38:10'),(331,10,10,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(332,10,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(333,10,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(334,10,4,1,'2025-04-16 06:39:23','2025-04-16 09:38:10'),(335,11,10,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(336,11,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(337,11,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(338,11,4,0,'2025-04-16 06:39:23','2025-04-16 09:33:29'),(339,12,10,0,'2025-04-16 06:39:23','2025-04-28 05:50:55'),(340,12,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(341,12,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(342,12,4,1,'2025-04-16 06:39:23','2025-04-16 09:38:10'),(343,13,10,0,'2025-04-16 06:39:23','2025-04-28 05:50:56'),(344,13,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(345,13,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(346,13,4,1,'2025-04-16 06:39:23','2025-04-28 06:03:36'),(347,14,10,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(348,14,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(349,14,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(350,14,4,1,'2025-04-16 06:39:23','2025-04-16 09:38:10'),(351,15,10,0,'2025-04-16 06:39:23','2025-04-28 05:50:56'),(352,15,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(353,15,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(354,15,4,1,'2025-04-16 06:39:23','2025-04-16 09:38:10'),(355,47,10,0,'2025-04-16 06:39:23','2025-04-28 05:52:52'),(356,47,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(357,47,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(358,47,4,1,'2025-04-16 06:39:23','2025-04-28 06:04:53'),(359,48,10,0,'2025-04-16 06:39:23','2025-04-28 05:52:53'),(360,48,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(361,48,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(362,48,4,1,'2025-04-16 06:39:23','2025-04-16 09:38:10'),(363,49,10,0,'2025-04-16 06:39:23','2025-04-28 05:52:53'),(364,49,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(365,49,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(366,49,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(367,50,10,0,'2025-04-16 06:39:23','2025-04-28 05:52:53'),(368,50,6,1,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(369,50,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(370,50,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(375,52,10,0,'2025-04-16 06:39:23','2025-04-28 05:52:53'),(376,52,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(377,52,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(378,52,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(379,53,10,0,'2025-04-16 06:39:23','2025-04-28 05:52:53'),(380,53,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(381,53,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(382,53,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(383,54,10,0,'2025-04-16 06:39:23','2025-04-28 05:52:53'),(384,54,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(385,54,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(386,54,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(387,16,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:47'),(388,16,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(389,16,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(390,16,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(391,17,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:48'),(392,17,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(393,17,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(394,17,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(395,18,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:48'),(396,18,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(397,18,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(398,18,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(399,19,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:48'),(400,19,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(401,19,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(402,19,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(407,21,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:48'),(408,21,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(409,21,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(410,21,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(411,22,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:48'),(412,22,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(413,22,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(414,22,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(415,23,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:48'),(416,23,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(417,23,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(418,23,4,1,'2025-04-16 06:39:23','2025-04-16 09:38:10'),(419,24,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:48'),(420,24,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(421,24,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(422,24,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(423,25,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:48'),(424,25,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(425,25,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(426,25,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(427,26,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:48'),(428,26,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(429,26,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(430,26,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(431,27,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:48'),(432,27,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(433,27,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(434,27,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(435,28,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:48'),(436,28,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(437,28,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(438,28,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(439,29,10,1,'2025-04-16 06:39:23','2025-04-16 06:39:23'),(440,29,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(441,29,5,1,'2025-04-16 06:39:23','2025-04-16 09:45:39'),(442,29,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(443,30,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:48'),(444,30,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(445,30,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(446,30,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(447,31,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:49'),(448,31,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(449,31,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(450,31,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(451,32,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:49'),(452,32,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(453,32,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(454,32,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(455,33,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:49'),(456,33,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(457,33,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(458,33,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(459,34,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:49'),(460,34,6,0,'2025-04-16 06:39:23','2025-04-16 09:51:40'),(461,34,5,0,'2025-04-16 06:39:23','2025-04-16 09:41:49'),(462,34,4,0,'2025-04-16 06:39:23','2025-04-16 09:26:34'),(463,35,10,0,'2025-04-16 06:39:23','2025-04-28 07:17:49'),(464,35,6,0,'2025-04-16 06:39:23','2025-05-08 06:43:37'),(465,35,5,0,'2025-04-16 06:39:23','2025-05-08 06:43:36'),(466,35,4,0,'2025-04-16 06:39:23','2025-05-08 06:43:36'),(479,4,3,0,'2025-04-16 07:11:21','2025-04-16 09:05:15'),(480,63,2,1,'2025-04-28 03:26:54','2025-04-28 03:26:54'),(481,63,7,1,'2025-04-28 03:26:54','2025-04-28 03:26:54'),(482,63,8,1,'2025-04-28 03:26:54','2025-04-28 03:26:54'),(483,63,9,1,'2025-04-28 03:26:54','2025-04-28 03:26:54'),(484,63,11,1,'2025-04-28 03:26:54','2025-04-28 03:26:54'),(487,5,3,1,'2025-04-28 05:56:09','2025-04-28 05:56:53'),(488,12,3,0,'2025-04-28 06:02:01','2025-04-28 06:02:49'),(489,63,1,1,'2025-04-28 06:11:26','2025-04-28 06:11:26'),(490,63,3,1,'2025-04-28 06:11:26','2025-04-28 06:11:26'),(491,63,4,1,'2025-04-28 06:11:26','2025-04-28 06:11:26'),(492,63,5,1,'2025-04-28 06:11:26','2025-04-28 06:11:26'),(493,63,6,1,'2025-04-28 06:11:26','2025-04-28 06:11:26'),(494,63,10,0,'2025-04-28 06:11:26','2025-04-28 06:11:26'),(502,20,1,1,'2025-04-28 07:23:16','2025-04-28 07:23:16'),(503,20,2,1,'2025-04-28 07:23:22','2025-04-28 07:23:22'),(504,20,7,1,'2025-04-28 07:23:29','2025-04-28 07:23:29'),(505,20,8,1,'2025-04-28 07:23:36','2025-04-28 07:23:36'),(506,20,9,1,'2025-04-28 07:23:44','2025-04-28 07:23:44'),(507,20,11,1,'2025-04-28 07:23:50','2025-04-28 07:23:50'),(508,1,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(509,2,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(510,3,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(511,4,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(512,5,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(513,6,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(514,7,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(515,8,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(516,9,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(517,10,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(518,11,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(519,12,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(520,13,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(521,14,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(522,15,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(523,47,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(524,48,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(525,49,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(526,50,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(527,52,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(528,53,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(529,54,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(530,63,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(531,16,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(532,17,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(533,18,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(534,19,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(535,20,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(536,21,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(537,22,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(538,23,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(539,24,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(540,25,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(541,26,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(542,27,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(543,28,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(544,29,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(545,30,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(546,31,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(547,32,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(548,33,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(549,34,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30'),(550,35,12,1,'2025-05-08 06:05:30','2025-05-08 06:05:30');
/*!40000 ALTER TABLE `tenant_service` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `tenant_service_view`
--

DROP TABLE IF EXISTS `tenant_service_view`;
/*!50001 DROP VIEW IF EXISTS `tenant_service_view`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `tenant_service_view` AS SELECT 
 1 AS `客户名`,
 1 AS `客户英文名`,
 1 AS `环境名`,
 1 AS `租户名`,
 1 AS `租户ID`,
 1 AS `ServiceID`,
 1 AS `Service名`,
 1 AS `是否启用`,
 1 AS `创建时间`,
 1 AS `更新时间`*/;
SET character_set_client = @saved_cs_client;

--
-- Dumping events for database 'release'
--

--
-- Dumping routines for database 'release'
--
